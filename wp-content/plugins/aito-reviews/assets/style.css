/* AITO Reviews Plugin Styles */

/* Admin Styles */
.aito-reviews-actions {
    margin-top: 20px;
    padding: 20px;
    background: #fff;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.aito-reviews-actions h3 {
    margin-top: 0;
}

.aito-reviews-actions .button {
    margin-right: 10px;
}

#aito-action-results {
    margin-top: 15px;
}

#aito-action-results .notice {
    margin: 0;
}

#aito-action-results code {
    background: #f1f1f1;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 12px;
}

#aito-action-results ul {
    margin: 10px 0;
    padding-left: 20px;
}

.aito-status-info {
    background: #f9f9f9;
    padding: 15px;
    border-left: 4px solid #0073aa;
    margin: 15px 0;
}

.aito-status-info h4 {
    margin-top: 0;
    color: #23282d;
}

.aito-status-info p {
    margin: 8px 0;
}

/* Cron Status Styles */
.aito-cron-status {
    background: #f9f9f9;
    padding: 15px;
    border-left: 4px solid #0073aa;
    margin: 15px 0;
}

.aito-cron-status h4 {
    margin-top: 0;
    color: #23282d;
}

.aito-cron-status p {
    margin: 8px 0;
}

.aito-cron-status .aito-success {
    color: #00a32a;
    font-weight: bold;
}

.aito-cron-status .aito-error {
    color: #d63638;
    font-weight: bold;
}

.aito-cron-status .aito-warning {
    color: #dba617;
    font-weight: bold;
}

.aito-cron-status em {
    font-size: 0.9em;
    color: #666;
}

.aito-system-info {
    background: #f9f9f9;
    padding: 15px;
    border-left: 4px solid #0073aa;
    margin: 15px 0;
}

.aito-system-info h4 {
    margin-top: 0;
    color: #23282d;
}

.aito-system-info table {
    margin-top: 10px;
}

.aito-system-info .aito-success {
    color: #00a32a;
    font-weight: bold;
}

.aito-system-info .aito-error {
    color: #d63638;
    font-weight: bold;
}

.aito-system-info .notice.inline {
    margin: 15px 0 0 0;
    padding: 8px 12px;
}

/* Debug Information Styles */
.aito-debug-info {
    margin-top: 15px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f9f9f9;
}

.aito-debug-info.success {
    border-left: 4px solid #00a32a;
}

.aito-debug-info.error {
    border-left: 4px solid #d63638;
}

.aito-debug-info h4 {
    margin: 0 0 10px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.aito-debug-info h5 {
    margin: 15px 0 5px 0;
    color: #23282d;
    font-size: 14px;
}

.aito-debug-info h6 {
    margin: 10px 0 5px 0;
    color: #666;
    font-size: 13px;
}

.aito-debug-info ul {
    margin: 5px 0 15px 20px;
}

.aito-debug-info li {
    margin: 3px 0;
    font-size: 13px;
}

.aito-debug-info pre {
    background: #fff;
    border: 1px solid #ddd;
    padding: 10px;
    border-radius: 3px;
    font-size: 12px;
    line-height: 1.4;
    max-height: 300px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.aito-debug-info .stack-trace {
    max-height: 200px;
    font-size: 11px;
}

.aito-debug-info .error-message {
    color: #d63638;
    font-weight: bold;
    background: #fff;
    padding: 8px;
    border-radius: 3px;
    border: 1px solid #f5c6cb;
}

.aito-toggle-debug {
    font-size: 12px;
    text-decoration: none;
    color: #0073aa;
}

.aito-toggle-debug:hover {
    color: #005a87;
}

.aito-debug-content {
    margin-top: 10px;
}

/* Responsive debug info */
@media (max-width: 768px) {
    .aito-debug-info pre {
        font-size: 11px;
        max-height: 200px;
    }

    .aito-debug-info h4 {
        flex-direction: column;
        align-items: flex-start;
    }

    .aito-toggle-debug {
        margin-top: 5px;
    }
}

/* Debug Log Section */
.aito-debug-log-section {
    margin-top: 20px;
    padding: 20px;
    background: #fff;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.aito-debug-log-section h3 {
    margin-top: 0;
    color: #23282d;
}

.debug-log-content {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
}

.debug-entry {
    padding: 2px 0;
    border-bottom: 1px solid #eee;
    word-wrap: break-word;
}

.debug-entry:last-child {
    border-bottom: none;
}

.debug-entry:hover {
    background: #f0f0f0;
}

/* Detected Methods Container */
.aito-detected-methods {
    max-width: 100%;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.aito-detected-methods code {
    display: inline-block;
    margin: 2px 4px 2px 0;
    padding: 2px 6px;
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 3px;
    font-size: 12px;
    line-height: 1.4;
    word-break: break-all;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.aito-detected-methods code:hover {
    white-space: normal;
    max-width: none;
    overflow: visible;
    z-index: 10;
    position: relative;
    background: #d4edda;
    border-color: #c3e6cb;
}

/* Method list containers */
.aito-method-list {
    max-height: 120px;
    overflow-y: auto;
    background: #fff;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    margin: 8px 0;
    line-height: 1.6;
}

.aito-method-list code {
    display: inline-block;
    margin: 2px 3px;
    padding: 1px 4px;
    font-size: 11px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 2px;
    word-break: break-all;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.aito-method-list code:hover {
    white-space: normal;
    max-width: none;
    overflow: visible;
    background: #e2e6ea;
    z-index: 5;
    position: relative;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .aito-detected-methods {
        font-size: 14px;
    }

    .aito-detected-methods code {
        font-size: 11px;
        max-width: 120px;
        margin: 1px 2px;
        padding: 1px 4px;
    }

    .aito-method-list code {
        font-size: 10px;
        max-width: 100px;
        margin: 1px 2px;
    }
}

/* Frontend Styles */
.aito-review-summary {
    display: inline-block;
    font-family: inherit;
}

.aito-review-summary.no-data {
    color: #666;
    font-style: italic;
}

.aito-review-score {
    font-weight: bold;
    font-size: 1.2em;
    color: #0073aa;
    margin-right: 8px;
}

.aito-review-total {
    color: #666;
    margin-right: 8px;
}

.aito-review-update {
    font-size: 0.9em;
    color: #999;
}

/* Star Rating Styles */
.aito-star-rating {
    display: inline-block;
    font-size: 1.2em;
    line-height: 1;
}

.aito-star {
    display: inline-block;
    color: #ddd;
    margin-right: 2px;
    transition: color 0.2s ease;
}

.aito-star.filled {
    color: #ffb900;
}

.aito-star.half {
    color: #ffb900;
    position: relative;
}

.aito-star.half::after {
    content: '★';
    position: absolute;
    left: 0;
    top: 0;
    width: 50%;
    overflow: hidden;
    color: #ddd;
}

.aito-star.empty {
    color: #ddd;
}

/* Shortcode Styles */
.aito-reviews-shortcode {
    display: inline-block;
}

.aito-reviews-shortcode .aito-review-score {
    font-size: inherit;
}

/* Responsive Design */
@media (max-width: 768px) {
    .aito-review-summary {
        display: block;
    }
    
    .aito-review-score,
    .aito-review-total,
    .aito-review-update {
        display: block;
        margin: 4px 0;
    }
    
    .aito-star-rating {
        font-size: 1em;
    }
}

/* Widget Styles */
.widget .aito-review-summary {
    text-align: center;
    padding: 10px;
}

.widget .aito-review-score {
    display: block;
    font-size: 2em;
    margin-bottom: 5px;
}

.widget .aito-review-total {
    display: block;
    font-size: 0.9em;
}

/* Card Style */
.aito-review-card {
    background: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.aito-review-card .aito-review-score {
    display: block;
    font-size: 3em;
    margin-bottom: 10px;
    color: #0073aa;
}

.aito-review-card .aito-review-total {
    display: block;
    font-size: 1.1em;
    margin-bottom: 5px;
    color: #333;
}

.aito-review-card .aito-star-rating {
    margin: 10px 0;
    font-size: 1.5em;
}

/* Inline Style */
.aito-review-inline {
    display: inline;
}

.aito-review-inline .aito-review-score,
.aito-review-inline .aito-review-total {
    display: inline;
    margin-right: 5px;
}

/* Loading States */
.aito-loading {
    opacity: 0.6;
    pointer-events: none;
}

.aito-loading::after {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #0073aa;
    border-radius: 50%;
    border-top-color: transparent;
    animation: aito-spin 1s linear infinite;
    margin-left: 8px;
    vertical-align: middle;
}

@keyframes aito-spin {
    to {
        transform: rotate(360deg);
    }
}

/* Error States */
.aito-error {
    color: #d63638;
    font-style: italic;
}

/* Success States */
.aito-success {
    color: #00a32a;
}

/* Accessibility */
.aito-star[aria-hidden="true"] {
    speak: none;
}

.screen-reader-text {
    clip: rect(1px, 1px, 1px, 1px);
    position: absolute !important;
    height: 1px;
    width: 1px;
    overflow: hidden;
}

/* Print Styles */
@media print {
    .aito-review-summary {
        color: #000 !important;
    }
    
    .aito-star {
        color: #000 !important;
    }
}
