{"key": "group_687a69e6d320d", "title": "Steps Ahead Fields", "fields": [{"key": "field_6855467c44d41", "label": "Steps Ahead Global Content", "name": "steps_ahead_global_content", "aria-label": "", "type": "group", "instructions": "This content will be used on all pages that have Steps Ahead panels enabled. Individual pages only control styling and on/off settings.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_68556e81d1903", "label": "Panel Status", "name": "panel_status", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "", "default_value": 0, "ui_on_text": "On", "ui_off_text": "Off", "ui": 1}, {"key": "field_68554280b1b27", "label": "Panel Title", "name": "panel_title", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_68556e81d1903", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_68554294b1b28", "label": "Columns", "name": "columns", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_68556e81d1903", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "pagination": 0, "min": 3, "max": 4, "collapsed": "field_685542b4b1b29", "button_label": "Add Column", "rows_per_page": 20, "sub_fields": [{"key": "field_685542b4b1b29", "label": "Title", "name": "title", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_68554294b1b28"}, {"key": "field_685542c0b1b2a", "label": "Image", "name": "image", "aria-label": "", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "return_format": "array", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "", "preview_size": "medium", "parent_repeater": "field_68554294b1b28"}, {"key": "field_685542d7b1b2b", "label": "Points", "name": "points", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "table", "min": 0, "max": 0, "collapsed": "field_685542fab1b2c", "button_label": "Add Point", "rows_per_page": 20, "parent_repeater": "field_68554294b1b28", "sub_fields": [{"key": "field_685542fab1b2c", "label": "Title", "name": "title", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_685542d7b1b2b"}, {"key": "field_68554307b1b2d", "label": "Text", "name": "text", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "rows": 3, "placeholder": "", "new_lines": "", "parent_repeater": "field_685542d7b1b2b"}]}]}]}], "location": [[{"param": "options_page", "operator": "==", "value": "steps-ahead"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1752853954}